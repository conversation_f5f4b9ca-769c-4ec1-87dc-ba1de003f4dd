"""
flag_touch_json.py  –  v2  (adds LONG_DWELL and SIM_TOUCH_CLUSTER)

USAGE
  python flag.py                           # Process all JSON files in raw_data/ directory
  python flag.py -o custom_output_dir      # Process files and output to custom directory
  # thresholds can be tweaked at the top of the file
"""

import json, math, sys, gzip, os
from pathlib import Path
from collections import defaultdict, deque

# ── CONFIGURABLE THRESHOLDS ─────────────────────────────────────────────────────
MAX_JUMP_PX           = 200         # positional jump (existing)
MAX_VELOCITY_PX_S     = 6000        # planar speed   (existing)
ACC_SPIKE_G           = 2.0         # g-force spike  (existing)
PALM_RADIUS_PX        = 30          # palm size      (existing)

LONG_DWELL_SEC        = 0.75        # ≥750 ms counts as a long press
DWELL_MOVE_TOL_PX     = 15          # must wander ≤15 px during the press

CLUSTER_WINDOW_SEC    = 0.050       # 50 ms between Begans ⇒ same cluster
CLUSTER_RADIUS_PX     = 100         # Began events within 100 px of each other
# ────────────────────────────────────────────────────────────────────────────────

def load_json(path):
    opener = gzip.open if str(path).endswith((".gz", ".gzip")) else open
    with opener(path, "rt", encoding="utf-8") as fh:
        return json.load(fh)

def mag3(ax, ay, az):
    return math.sqrt(ax*ax + ay*ay + az*az)

def flag_events(data):
    # compute screen bounds once (1st–99th percentiles) for OUT_OF_BOUNDS rule
    xs, ys = [], []
    for seq in data.values():
        xs.extend(e["x"] for e in seq)
        ys.extend(e["y"] for e in seq)
    xs.sort(); ys.sort()
    xmin, xmax = xs[int(0.01*len(xs))], xs[int(0.99*len(xs))]
    ymin, ymax = ys[int(0.01*len(ys))], ys[int(0.99*len(ys))]
    
    # ---------- pass 1: finger-local flagging & metrics ----------
    last = defaultdict(lambda: {"x":None,"y":None,"t":None})
    seq_info = defaultdict(lambda: {
        "t0": None, "tN": None,
        "x_min": float("inf"), "x_max": float("-inf"),
        "y_min": float("inf"), "y_max": float("-inf"),
        "events": []
    })
    
    for seq_id, seq in data.items():
        for ev in seq:
            fid  = ev.get("fingerId")
            x, y = ev["x"], ev["y"]
            t    = ev["time"]
            flags = []
            
            # ----- per-event rules (existing ones) -----
            if fid is None or fid < 0:
                flags.append("NEG_FINGER_ID")
            if not (xmin <= x <= xmax and ymin <= y <= ymax):
                flags.append("OUT_OF_BOUNDS")
            
            l = last[fid]
            if l["t"] is not None:
                dt = t - l["t"]
                dx = x - l["x"]
                dy = y - l["y"]
                if dt < 0:
                    flags.append("TIME_REVERSAL")
                elif dt == 0:
                    flags.append("DUP_TS")
                if abs(dx) >= MAX_JUMP_PX or abs(dy) >= MAX_JUMP_PX:
                    flags.append("JUMP_XY")
                if dt > 1e-4 and math.hypot(dx,dy)/dt >= MAX_VELOCITY_PX_S:
                    flags.append("HIGH_VEL")
            if all(k in ev for k in ("accx","accy","accz")):
                if mag3(ev["accx"],ev["accy"],ev["accz"]) >= ACC_SPIKE_G:
                    flags.append("ACC_SPIKE")
            r = ev.get("radius") or ev.get("majorAxis")
            if r and r >= PALM_RADIUS_PX:
                flags.append("PALM_RADIUS")
            if ev.get("touchPhase") not in {"Began","Moved",
                                            "Stationary","Ended","Canceled"}:
                flags.append("UNKNOWN_PHASE")
            
            ev["flags"] = flags
            last[fid]   = {"x":x, "y":y, "t":t}
            
            # accumulate stats for long-dwell analysis
            info = seq_info[(fid, seq_id)]
            info["events"].append(ev)
            if info["t0"] is None: info["t0"] = t
            info["tN"] = t
            info["x_min"] = min(info["x_min"], x)
            info["x_max"] = max(info["x_max"], x)
            info["y_min"] = min(info["y_min"], y)
            info["y_max"] = max(info["y_max"], y)
    
    # ---------- pass 2: flag long-press dwells ----------
    for info in seq_info.values():
        duration = info["tN"] - info["t0"]
        dx = info["x_max"] - info["x_min"]
        dy = info["y_max"] - info["y_min"]
        if (duration >= LONG_DWELL_SEC and
            max(dx,dy) <= DWELL_MOVE_TOL_PX):
            for ev in info["events"]:
                ev["flags"].append("LONG_DWELL")
    
    # ---------- pass 3: flag simultaneous-touch clusters ----------
    # gather all Began events
    began = []
    for seq in data.values():
        for ev in seq:
            if ev["touchPhase"] == "Began":
                began.append(ev)
    # sort Begans by time
    began.sort(key=lambda e: e["time"])
    
    # sweep-line clustering
    window = deque()  # events within CLUSTER_WINDOW_SEC
    for ev in began:
        t = ev["time"]
        # discard from window any Begans too old
        while window and t - window[0]["time"] > CLUSTER_WINDOW_SEC:
            window.popleft()
        # compare against current window for spatial proximity
        cluster_mates = [e for e in window
                         if math.hypot(e["x"]-ev["x"], e["y"]-ev["y"])
                            <= CLUSTER_RADIUS_PX]
        if cluster_mates:
            ev["flags"].append("SIM_TOUCH_CLUSTER")
            for mate in cluster_mates:
                mate["flags"].append("SIM_TOUCH_CLUSTER")
        window.append(ev)
    
    return data

# ---------------------------- CLI wrapper --------------------------------------
def process_single_file(src_path, dst_path):
    """Process a single JSON file and save the flagged result."""
    try:
        doc = load_json(src_path)

        # Handle different JSON structures
        if "touchData" in doc:
            # Direct touchData structure
            doc["touchData"] = flag_events(doc["touchData"])
        elif "json" in doc and "touchData" in doc["json"]:
            # Nested structure: doc.json.touchData
            doc["json"]["touchData"] = flag_events(doc["json"]["touchData"])
        else:
            raise ValueError("No touchData found in JSON structure")

        out = json.dumps(doc, ensure_ascii=False, indent=2)
        with open(dst_path, "w", encoding="utf-8") as fh:
            fh.write(out)
        return True
    except Exception as e:
        print(f"Error processing {src_path}: {e}", file=sys.stderr)
        return False

def find_json_files(directory):
    """Find all JSON and JSON.gz files in the given directory."""
    json_files = []
    directory = Path(directory)

    if not directory.exists():
        return json_files

    # Find .json and .json.gz files
    for pattern in ["*.json", "*.json.gz"]:
        json_files.extend(directory.glob(pattern))

    return sorted(json_files)

def main():
    # Parse command line arguments
    output_dir = "flagged_data"

    # Check for help option
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help"]:
        print("Usage: python flag.py [-o output_directory]")
        print("  Processes all JSON files in raw_data/ directory")
        print("  -o: specify output directory (default: flagged_data)")
        print("  -h, --help: show this help message")
        sys.exit(0)

    # Check for -o option
    if len(sys.argv) >= 3 and sys.argv[1] == "-o":
        output_dir = sys.argv[2]
    elif len(sys.argv) > 1:
        print("Usage: python flag.py [-o output_directory]", file=sys.stderr)
        print("  Processes all JSON files in raw_data/ directory", file=sys.stderr)
        print("  -o: specify output directory (default: flagged_data)", file=sys.stderr)
        print("  -h, --help: show this help message", file=sys.stderr)
        sys.exit(1)

    # Set up directories
    input_dir = Path("raw_data")
    output_dir = Path(output_dir)

    # Check if input directory exists
    if not input_dir.exists():
        print(f"Error: Input directory '{input_dir}' does not exist.", file=sys.stderr)
        print("Please create the 'raw_data' directory and place JSON files in it.", file=sys.stderr)
        sys.exit(1)

    # Find JSON files
    json_files = find_json_files(input_dir)

    if not json_files:
        print(f"Error: No JSON files found in '{input_dir}' directory.", file=sys.stderr)
        print("Please place .json or .json.gz files in the raw_data directory.", file=sys.stderr)
        sys.exit(1)

    # Create output directory if it doesn't exist
    output_dir.mkdir(exist_ok=True)

    # Process each file
    processed_count = 0
    failed_count = 0

    print(f"Found {len(json_files)} JSON file(s) in '{input_dir}'")
    print(f"Processing files and saving to '{output_dir}'...")

    for src_file in json_files:
        # Preserve original filename in output directory
        dst_file = output_dir / src_file.name

        print(f"Processing: {src_file.name}")

        if process_single_file(src_file, dst_file):
            processed_count += 1
            print(f"  ✓ Saved to: {dst_file}")
        else:
            failed_count += 1
            print(f"  ✗ Failed to process: {src_file.name}")

    print(f"\nProcessing complete:")
    print(f"  Successfully processed: {processed_count} files")
    if failed_count > 0:
        print(f"  Failed: {failed_count} files")

    if failed_count > 0:
        sys.exit(1)

if __name__ == "__main__":
    main()
